package channel

import (
	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
	"github.com/samber/lo"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
)

// ChannelReadScope 返回一个GORM查询范围函数，用于限制用户只能查看自己及下级渠道的数据
// 如果用户有特权且请求包含privileged参数，则不限制查询范围
func ChannelReadScope(userID uint64, canPrivileged bool, privileged bool) func(tx *gorm.DB) *gorm.DB {
	if privileged && canPrivileged {
		return func(tx *gorm.DB) *gorm.DB {
			return tx
		}
	}

	return func(tx *gorm.DB) *gorm.DB {
		// 获取当前用户及其所有下级节点的ID列表
		tree, err := GetChannelChildren(userID)
		userIds := []uint64{}

		if err == nil && tree != nil {
			// 递归获取所有下级节点的ID
			allUserIds := tree.GetAllDescendantIDs()
			if len(allUserIds) > 0 {
				userIds = allUserIds
			}
		}

		return tx.
			Where("user_id IN ?", userIds).
			Where("user_id != ?", userID).
			Where("channel_type = ? OR channel_type = ?", model.ChannelTypeDefault, model.ChannelTypeNormal)
	}
}

// IsUserInChannelTree 检查指定的渠道ID是否在用户的渠道树中
func IsUserInChannelTree(userID uint64, channelID uint64, hasGlobalPermission bool) bool {
	// 如果有全局权限，则直接返回true
	if hasGlobalPermission {
		return true
	}

	var ids []uint64
	// 获取渠道树
	tree, err := GetChannelTree(userID)
	if err != nil {
		ids = append(ids, userID)
	} else {
		// 获取当前用户及其所有下级节点的ID
		ids = tree.GetAllDescendantIDs()
	}

	// 判断请求的渠道是否在树中
	return lo.Contains(ids, channelID)
}

// GetChannelByID 根据ID获取渠道信息
func GetChannelByID(id uint64, scope func(tx *gorm.DB) *gorm.DB) (*model.User, error) {
	userChannel := &model.User{}
	db := cosy.UseDB()

	err := db.Model(&model.User{}).
		Scopes(scope).
		Joins("LEFT JOIN channels ON channels.user_id = users.id").
		Where("users.id = ?", id).
		Scan(userChannel).
		Error

	return userChannel, err
}

// ModifyChannelByID 更新渠道信息
func ModifyChannelByID(id uint64) error {
	q := query.Channel

	_, err := q.Where(q.UserID.Eq(id)).FirstOrCreate()
	return err
}

// CreateChannelRelation 创建渠道关系
func CreateChannelRelation(rootUserID uint64, userID uint64, relateUserID uint64) error {
	q := query.Channel

	// 防止循环路径
	if userID == relateUserID {
		return ErrCircleSubordinate
	}

	// 在每个实体中，每个节点的入度应小于或等于1。
	// 所以，要检查入度，只需计算relate_user_id = json.RelateUserID的记录
	inDegree, _ := q.Where(q.RelateUserID.Eq(relateUserID)).Count()

	if inDegree >= 1 {
		return ErrChannelDuplicate
	}

	chn, err := q.
		Where(q.UserID.Eq(userID)).
		Where(q.RelateUserID.Eq(relateUserID)).
		FirstOrCreate()

	if err != nil {
		return err
	}

	chnRoot, _ := q.
		Where(q.UserID.Eq(userID)).
		Or(q.RelateUserID.Eq(userID)).First()

	if chnRoot != nil {
		rootUserID = chnRoot.RootUserID
		_, err = q.
			Where(q.ID.Eq(chn.ID)).Update(q.RootUserID, rootUserID)

		if err != nil {
			return err
		}
	}

	_, err = q.
		Where(q.UserID.Eq(relateUserID)).
		Or(q.RootUserID.Eq(relateUserID)).
		Update(q.RootUserID, rootUserID)

	return err
}

// DestroyChannelRelation 删除渠道关系
func DestroyChannelRelation(rootUserID uint64, relateUserID uint64) error {
	db := cosy.UseDB()
	q := query.Use(db)

	tx := q.Begin()

	chn := query.Channel

	node, err := chn.Where(chn.RootUserID.Eq(rootUserID),
		chn.RelateUserID.Eq(relateUserID)).
		First()

	if err != nil {
		return err
	}

	// 不允许删除根节点
	if node.RootUserID == node.UserID && node.RelateUserID == 0 {
		tx.Rollback()
		return ErrCannotRemoveRootNode
	}

	// 删除目标节点
	_, err = chn.
		Where(chn.ID.Eq(node.ID)).
		Delete()

	if err != nil {
		tx.Rollback()
		return err
	}

	// 重新构建树
	tree, err := GetChannelTree(node.RootUserID)
	if err != nil {
		tx.Rollback()
		return err
	}

	// 将删除节点的所有下级节点，重新关联到删除节点的上一级
	for _, descendant := range tree.GetAllDescendants(node.RootUserID) {
		_, err = chn.
			Where(chn.UserID.Eq(descendant.CurrentID)).
			Update(chn.RootUserID, rootUserID)

		if err != nil {
			tx.Rollback()
			return err
		}
	}

	tx.Commit()
	return nil
}
